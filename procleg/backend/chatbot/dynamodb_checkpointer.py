import json
import os
import time
import uuid
from datetime import datetime, timezone
from typing import Any, NamedTuple, Union
from collections.abc import Sequence

from dotenv import load_dotenv
from procleg.backend.services.conversation_thread_services import ConversationThreadModel
from procleg.backend.services import s3_services
from procleg.logger_config import get_logger
from langchain_core.runnables import RunnableConfig

# Import necessary types from langgraph
try:
    from langgraph.checkpoint.base import CheckpointTuple, CheckpointMetadata, Checkpoint
    from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer
    # ChannelVersions is typically a type alias for Dict[str, Union[int, float, str]]
    try:
        from langgraph.checkpoint.base import ChannelVersions
    except ImportError:
        # Define fallback type if ChannelVersions import fails
        ChannelVersions = dict[str, Union[int, float, str]]
except ImportError:
    # Define fallback types if imports fail
    class CheckpointMetadata(dict[str, Any]):  # type: ignore
        pass


    class Checkpoint(dict[str, Any]):  # type: ignore
        pass


    class CheckpointTuple(NamedTuple):  # type: ignore
        config: RunnableConfig
        checkpoint: Checkpoint
        metadata: CheckpointMetadata
        parent_config: RunnableConfig | None = None
        pending_writes: list | None = None

    # Define fallback type for ChannelVersions
    ChannelVersions = dict[str, Union[int, float, str]]

    # Define fallback JsonPlusSerializer
    class JsonPlusSerializer:
        def dumps(self, obj):
            # Simple fallback serialization that returns bytes for S3 storage
            def default_serializer(o):
                if hasattr(o, '__dict__'):
                    return o.__dict__
                elif hasattr(o, '__class__'):
                    # Handle special objects like messages
                    return str(o)
                return str(o)

            try:
                json_str = json.dumps(obj, default=default_serializer, ensure_ascii=False)
                # Return bytes for consistency with real JsonPlusSerializer
                return json_str.encode('utf-8')
            except Exception as e:
                logger.error(f"Fallback serialization failed: {e}")
                return None

        def loads(self, data):
            try:
                if isinstance(data, bytes):
                    data = data.decode('utf-8')
                return json.loads(data)
            except Exception as e:
                logger.error(f"Fallback deserialization failed: {e}")
                return None

logger = get_logger(__name__)
load_dotenv()

# S3 Configuration for checkpoint storage
S3_BUCKET_NAME = os.getenv('S3_BUCKET', 'aig-azcdi-us-alxn-procleg-ds-dev')
S3_CHECKPOINT_PREFIX = "langgraph_checkpoints/"
S3_WRITES_PREFIX = "langgraph_writes/"

LANGGRAPH_MODULE = "langgraph_state"
LANGGRAPH_WRITES_MODULE = "langgraph_writes"

ERROR = "error"
SCHEDULED = "scheduled"
INTERRUPT = "interrupt"
RESUME = "resume"
WRITES_IDX_MAP = {ERROR: -1, SCHEDULED: -2, INTERRUPT: -3, RESUME: -4}


class DynamoDBCheckpointer:
    def __init__(self, max_retries=3, retry_delay=0.1, prid="system"):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.prid = prid
        # Use LangGraph's JsonPlusSerializer for proper binary serialization
        self.serde = JsonPlusSerializer()
        logger.info("DynamoDB checkpointer initialized with S3 storage using JsonPlusSerializer")

    def _generate_s3_key(self, thread_id: str, prefix: str, suffix: str = "") -> str:
        """Generate a unique S3 key for storing checkpoint data."""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        return f"{prefix}{thread_id}/{timestamp}_{unique_id}{suffix}.bin"

    def _store_data_in_s3(self, data: Any, s3_key: str) -> bool:
        """Store serialized data in S3 and return success status."""
        try:
            # Serialize data using JsonPlusSerializer (returns bytes)
            logger.debug(f"Serializing data for S3 key: {s3_key}")
            serialized_data = self.serde.dumps(data)

            if serialized_data is None:
                logger.error(f"Serialization returned None for S3 key: {s3_key}")
                return False

            logger.debug(f"Serialized data size: {len(serialized_data)} bytes")

            # Use direct S3 client approach for better control
            try:
                import boto3
                from boto3.session import Session

                # Get AWS configuration
                aws_profile = os.getenv('AWS_PROFILE')
                aws_region = os.getenv('AWS_REGION_NAME', 'us-east-1')

                if aws_profile:
                    session = Session(profile_name=aws_profile)
                    s3_client = session.client('s3', region_name=aws_region)
                else:
                    s3_client = boto3.client('s3', region_name=aws_region)

                # Store binary data directly in S3
                s3_client.put_object(
                    Bucket=S3_BUCKET_NAME,
                    Key=s3_key,
                    Body=serialized_data,
                    ContentType='application/octet-stream'
                )

                logger.debug(f"Successfully stored data in S3: {s3_key}")
                return True

            except Exception as s3_error:
                logger.error(f"Direct S3 upload failed for {s3_key}: {s3_error}")
                # Fallback to existing s3_services method
                return self._fallback_s3_store(serialized_data, s3_key)

        except Exception as e:
            logger.error(f"Error storing data in S3 key {s3_key}: {e}", exc_info=True)
            return False

    def _fallback_s3_store(self, serialized_data: bytes, s3_key: str) -> bool:
        """Fallback S3 storage using existing s3_services."""
        try:
            # Extract folder and file name from S3 key
            folder_name = os.path.dirname(s3_key)
            file_name = os.path.basename(s3_key)

            if not folder_name or not file_name:
                logger.error(f"Invalid S3 key structure: {s3_key}")
                return False

            logger.debug(f"Fallback: Storing data in S3 - folder: {folder_name}, file: {file_name}")

            # Store binary data in S3 using existing service
            success = s3_services.put_file_in_s3(
                folder_name=folder_name,
                file_name=file_name,
                file_content=serialized_data
            )

            if success:
                logger.debug(f"Fallback: Successfully stored data in S3: {s3_key}")
                return True
            else:
                logger.error(f"Fallback: Failed to store data in S3: {s3_key}")
                return False

        except Exception as e:
            logger.error(f"Fallback S3 storage failed for {s3_key}: {e}", exc_info=True)
            return False

    def _load_data_from_s3(self, s3_key: str) -> Any | None:
        """Load and deserialize data from S3."""
        try:
            logger.debug(f"Loading data from S3: {s3_key}")

            # Try direct S3 client approach first
            try:
                import boto3
                from boto3.session import Session

                # Get AWS configuration
                aws_profile = os.getenv('AWS_PROFILE')
                aws_region = os.getenv('AWS_REGION_NAME', 'us-east-1')

                if aws_profile:
                    session = Session(profile_name=aws_profile)
                    s3_client = session.client('s3', region_name=aws_region)
                else:
                    s3_client = boto3.client('s3', region_name=aws_region)

                # Read binary data from S3
                response = s3_client.get_object(Bucket=S3_BUCKET_NAME, Key=s3_key)
                binary_data = response['Body'].read()

            except Exception as s3_error:
                logger.warning(f"Direct S3 read failed for {s3_key}: {s3_error}, trying fallback")
                # Fallback to existing s3_services method
                binary_data = s3_services.read_from_s3_file(s3_key, S3_BUCKET_NAME)

            if binary_data is None:
                logger.error(f"Failed to read data from S3: {s3_key}")
                return None

            logger.debug(f"Read {len(binary_data)} bytes from S3: {s3_key}")

            # Deserialize using JsonPlusSerializer
            data = self.serde.loads(binary_data)
            if data is None:
                logger.error(f"Deserialization returned None for S3 key: {s3_key}")
                return None

            logger.debug(f"Successfully loaded and deserialized data from S3: {s3_key}")
            return data

        except Exception as e:
            logger.error(f"Error loading data from S3 key {s3_key}: {e}", exc_info=True)
            return None

    def get_tuple(self, config: RunnableConfig) -> CheckpointTuple | None:
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot retrieve checkpoint tuple")
            return None

        try:
            thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, LANGGRAPH_MODULE)

            if thread is None or thread.state_data is None:
                logger.debug(f"No checkpoint found in DynamoDB for thread_id: {thread_id}")
                return None  # LangGraph will initialize a new checkpoint for this config

            # The state_data now contains the S3 key instead of the actual data
            s3_key = thread.state_data

            # Load the actual data from S3
            saved_data = self._load_data_from_s3(s3_key)
            if saved_data is None:
                logger.error(f"Failed to load checkpoint data from S3 for thread_id: {thread_id}")
                return None

            checkpoint_dict = saved_data.get("checkpoint")
            retrieved_metadata_dict = saved_data.get("metadata")
            saved_data.get("new_versions", {})

            if not checkpoint_dict:
                logger.error(
                    f"Corrupted data: 'checkpoint' field missing in saved state for thread_id: {thread_id}")
                return None

            current_checkpoint: Checkpoint = checkpoint_dict

            # Construct CheckpointMetadata object
            # Default to values LangGraph expects for a new/initial state if metadata is missing/incomplete
            current_metadata = CheckpointMetadata(
                source="loop",  # A common source, can be updated if retrieved_metadata_dict has it
                step=-1,  # Crucial: default for pre-first-step or if missing
                writes={},
                score=None,
                source_ts=datetime.now(timezone.utc).isoformat()
            )

            if retrieved_metadata_dict:
                current_metadata.update(
                    retrieved_metadata_dict)  # Load all fields from saved metadata
                if "step" not in retrieved_metadata_dict:
                    # If metadata was saved but 'step' is missing (e.g. old format)
                    logger.warning(
                        f"'step' key missing in retrieved metadata for thread_id: {thread_id}. Defaulting step to -1.")
                    current_metadata["step"] = -1
            else:
                logger.warning(
                    f"No metadata found alongside checkpoint for thread_id: {thread_id}. Initializing with default step -1.")

            return CheckpointTuple(
                config=config,
                checkpoint=current_checkpoint,
                metadata=current_metadata,
                parent_config=None,
                pending_writes=None
            )
        except json.JSONDecodeError:
            logger.error(f"Failed to decode state_data for thread_id: {thread_id}. Returning None.",
                         exc_info=True)
            return None
        except Exception as e:
            logger.error(f"Error retrieving checkpoint tuple for thread_id {thread_id}: {e}",
                         exc_info=True)
            return None

    def get_next_version(self, current, channel):
        if isinstance(current, str):
            raise NotImplementedError("String versions not supported")
        elif current is None:
            return 1
        else:
            return current + 1

    # get(self, key: str) is not part of the standard BaseCheckpointSaver interface used by get_tuple.
    # get_tuple should directly fetch and process. If you have a separate get method, ensure it's
    # aligned with what get_tuple needs or refactor get_tuple to incorporate its logic.

    def put(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,  # langgraph.checkpoint.base.Checkpoint (which is a TypedDict)
        metadata: CheckpointMetadata,
        # langgraph.checkpoint.base.CheckpointMetadata (also TypedDict)
        new_versions: ChannelVersions,  # New channel versions as of this write
    ) -> RunnableConfig:  # Standard put returns RunnableConfig or Coroutine[RunnableConfig]
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot store checkpoint")
            return config

        # Data to be stored in S3
        data_to_store = {
            "checkpoint": checkpoint,
            "metadata": metadata,
            "new_versions": new_versions
        }

        # Generate S3 key for this checkpoint
        s3_key = self._generate_s3_key(thread_id, S3_CHECKPOINT_PREFIX, "_checkpoint")

        # Store data in S3
        if not self._store_data_in_s3(data_to_store, s3_key):
            logger.error(f"Failed to store checkpoint data in S3 for thread_id {thread_id}")
            raise RuntimeError(f"Failed to store checkpoint data in S3 for thread_id {thread_id}")

        retries = 0
        thread_name = f"LangGraph State {thread_id}"

        while retries < self.max_retries:
            try:
                thread = ConversationThreadModel.get_thread_by_primary_key(thread_id,
                                                                           LANGGRAPH_MODULE)

                if thread:
                    current_db_version = thread.version or 1  # For optimistic locking of the DB record
                    new_db_version = current_db_version + 1

                    actions = [
                        ConversationThreadModel.state_data.set(s3_key),  # Store S3 key instead of data
                        ConversationThreadModel.version.set(new_db_version),
                        ConversationThreadModel.updatedAt.set(datetime.now(timezone.utc))
                    ]
                    # PynamoDB's update raises a ConditionCheckFailedError if condition isn't met
                    thread.update(actions=actions,
                                  condition=(ConversationThreadModel.version == current_db_version))
                    logger.debug(
                        f"Updated state for thread_id: {thread_id} (db_version {current_db_version} -> {new_db_version})")
                    return config
                else:
                    ConversationThreadModel.create_thread(
                        thread_id=thread_id,
                        module=LANGGRAPH_MODULE,
                        prid=self.prid,
                        text_s3_url="",  # Not used for this type of state
                        last_modified_by="langgraph",
                        thread_name=thread_name,
                        state_data=s3_key,  # Store S3 key instead of data
                        version=1  # Initial DB record version
                    )
                    logger.debug(f"Created new state for thread_id: {thread_id}")
                    return config
            except Exception as e:  # Should be more specific, e.g., pynamodb.exceptions.UpdateError for condition failures
                retries += 1
                if retries >= self.max_retries:
                    logger.error(
                        f"Failed to save state for thread_id {thread_id} after {self.max_retries} attempts: {e}",
                        exc_info=True)
                    raise
                logger.warning(
                    f"Concurrency conflict or error saving state for thread_id {thread_id}, retrying ({retries}/{self.max_retries}): {e}")
                time.sleep(self.retry_delay * (2 ** retries))  # Exponential backoff
        # This line should ideally not be reached if an error is raised after max_retries
        return config

    def put_writes(
        self,
        config: RunnableConfig,
        writes: Sequence[tuple[str, Any]],
        task_id: str,
        # task_path: str = "", # task_path is not a standard arg for BaseCheckpointSaver.put_writes
    ) -> None:  # Standard put_writes returns None or Coroutine[None]
        thread_id = config.get("configurable", {}).get("thread_id", "")
        if not thread_id:
            logger.warning("No thread_id found in config, cannot store writes for task_id %s",
                           task_id)
            return

        module_for_writes = f"{LANGGRAPH_WRITES_MODULE}_{task_id}"  # Unique module per task

        writes_data_content = {}
        for i, (key_tuple_str, value) in enumerate(
            writes):  # Assuming writes are (str_representation_of_key_tuple, value)
            # Using a simple list of writes, as internal structure of writes can be complex
            # Storing them as a list [{ "key": "...", "value": ...}] might be better
            writes_data_content[f"write_{i}"] = {"key": key_tuple_str, "value": value}

        # Generate S3 key for writes data
        s3_key = self._generate_s3_key(thread_id, S3_WRITES_PREFIX, f"_writes_{task_id}")

        # Store writes data in S3
        if not self._store_data_in_s3(writes_data_content, s3_key):
            logger.error(f"Failed to store writes data in S3 for thread_id {thread_id}, task_id {task_id}")
            return

        try:
            thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module_for_writes)
            if thread:
                thread.update_thread(
                    state_data=s3_key,  # Store S3 key instead of data
                    last_modified_by="langgraph_writes",
                    thread_name=f"LangGraph Writes {thread_id}-{task_id}"
                )
                logger.debug(f"Updated writes for thread: {thread_id}, task: {task_id}")
            else:
                ConversationThreadModel.create_thread(
                    thread_id=thread_id,
                    module=module_for_writes,
                    prid=self.prid,  # Or derive from config if available/necessary
                    text_s3_url="",  # Not applicable
                    last_modified_by="langgraph_writes",
                    thread_name=f"LangGraph Writes {thread_id}-{task_id}",
                    state_data=s3_key,  # Store S3 key instead of data
                    version=1
                )
                logger.debug(f"Created new writes entry for thread: {thread_id}, task: {task_id}")
        except Exception as e:
            logger.error(f"Error storing writes for thread {thread_id}, task {task_id}: {e}",
                         exc_info=True)

    # async def aput_writes is also part of the interface, if you need async support.
    # For simplicity, it can call the sync version if true async DB operations aren't used.
    async def aput(
        self,
        config: RunnableConfig,
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: ChannelVersions,
    ) -> RunnableConfig:
        return self.put(config, checkpoint, metadata, new_versions)

    async def aget_tuple(self, config: RunnableConfig) -> CheckpointTuple | None:
        return self.get_tuple(config)

    async def aput_writes(
        self,
        config: RunnableConfig,
        writes: Sequence[tuple[str, Any]],
        task_id: str,
    ) -> None:
        return self.put_writes(config, writes, task_id)
