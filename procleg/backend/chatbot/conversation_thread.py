import os
import json
from datetime import datetime, timedelta
import concurrent.futures
from functools import lru_cache
from typing import Optional, List, Dict, Any, Literal
from dataclasses import dataclass, field
import time
from enum import Enum

from langchain_core.prompts import PromptTemplate
from pydantic import BaseModel

from procleg.backend.services import s3_services
from procleg.backend.services.conversation_thread_services import ConversationThreadModel
from dotenv import load_dotenv
from procleg.logger_config import get_logger
from procleg.backend.chatbot.models import llm

logger = get_logger(__name__)
load_dotenv()

S3_BUCKET = os.getenv('S3_BUCKET_NAME', 'aig-azcdi-us-alxn-procleg-ds-dev')

@lru_cache(maxsize=100, typed=True)
def _fetch_and_parse_thread(text_s3_url: str, thread_id: str) -> dict | None:
    """Fetch and parse a single conversation thread from S3 with caching.

    Args:
        text_s3_url (str): S3 URL of the conversation data
        thread_id (str): Thread ID for error logging

    Returns:
        Optional[dict]: Parsed conversation data or None if error occurs
    """
    try:
        s3_data = s3_services.read_from_s3_file(text_s3_url, S3_BUCKET)
        if not s3_data:
            logger.error("Failed to read S3 data for thread %s", thread_id)
            return None

        return json.loads(s3_data)
    except (json.JSONDecodeError, Exception) as e:
        logger.error("Error processing thread %s: %s", thread_id, e)
        return None

def load_conversations(prid: str, module: str = 'procurement') -> list[dict]:
    """Load conversation threads for a given project ID using parallel processing.

    Args:
        prid (str): Project ID to load conversations for
        module (str): Module name, defaults to 'procurement'

    Returns:
        list[dict]: List of conversation thread data dictionaries
    """
    logger.info("Loading conversations for PRID: %s and module %s", prid,module)

    try:
        threads = ConversationThreadModel.get_threads_by_prid_and_module(prid, module)
        if not threads:
            logger.warning("No conversation history found for PRID: %s", prid)
            return []

        conversations = []

        # Process threads in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(10, len(threads))) as executor:
            # Create futures for each thread
            future_to_thread = {
                executor.submit(_fetch_and_parse_thread, thread.text_s3_url, thread.thread_id): thread
                for thread in threads
            }

            # Process completed futures as they finish
            for future in concurrent.futures.as_completed(future_to_thread):
                thread = future_to_thread[future]
                try:
                    data = future.result()
                    if not data:
                        continue

                    thread_metadata = data[0] if data else {}
                    conversation_data = {
                        "thread_id": thread_metadata.get("thread_id", "unknown"),
                        "prid": prid,
                        "text": [
                            {
                                "role": message.get("role", ""),
                                "content": message.get("content", "")
                            }
                            for message in data[1:]
                        ],
                        "createdAt": thread_metadata.get("conversation_started_at",
                                                       datetime.now().isoformat()),
                        "thread_name": thread_metadata.get("thread_name", "unknown"),
                        "lastModifiedBy": prid,
                        "module": thread_metadata.get("module", "procurement")
                    }
                    conversations.append(conversation_data)
                    logger.debug("Loaded conversation thread: %s", conversation_data["thread_id"])
                except Exception as e:
                    logger.error("Error processing thread %s: %s", thread.thread_id, e)

        logger.info("Successfully loaded %d conversations", len(conversations))
        return conversations

    except Exception as e:
        logger.exception("Error loading conversations: %s", e)
        return []

def save_conversation(chat_history: list[dict[str, str]], prid: str, thread_id: str, module: str) -> bool:
    """Persist conversation history to JSON files.

    This function saves the conversation history to JSON files organized by PRID
    (Project ID) and thread ID. It appends new messages to existing conversations
    or creates new conversation files as needed.

    Args:
    ----
        chat_history (List[Dict[str, str]]): The conversation history to save
        prid (str): The Project ID for organizing conversation files
        thread_id (str): The thread ID for the specific conversation
        module (str): Module name for which the conversation is saved

    Returns:
    -------
        bool: True if successful, False if an error occurred
    """
    try:
        # Input validation
        if not all([chat_history, prid, thread_id, module]):
            logger.error("Missing required parameters")
            return False

        if not isinstance(chat_history, list):
            logger.error("Chat history must be a list")
            return False

        filename = f"conversation_{prid}_{thread_id}.json"
        file_path = f"/tmp/{filename}"

        # Get the latest exchange (last two messages)
        last_msgs_cnt = min(2, len(chat_history))  # Ensure we don't exceed list bounds
        latest_exchange = chat_history[-last_msgs_cnt:]

        logger.debug("Processing conversation thread: prid=%s, thread_id=%s", prid, thread_id)
        thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module)
        thread_name = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        if thread:
            thread_name=thread.thread_name
            logger.info("Found existing conversation thread: %s", thread_id)
            s3_data = s3_services.read_from_s3_file(thread.text_s3_url, S3_BUCKET)
            if not s3_data:
                logger.error("Failed to read existing conversation from S3")
                return False
            try:
                existing_data = json.loads(s3_data)
            except json.JSONDecodeError as e:
                logger.error("Failed to parse existing conversation data: %s", e)
                return False
        else:
            logger.info("Creating new conversation thread: %s", thread_id)
            if not chat_history:
                logger.error("Cannot create new thread without chat history")
                return False

            template = PromptTemplate(
                template="Generate ONE short, descriptive title (3-7 words) that reflects the topic or intent of this question: {user_query}",
                input_variables=['user_query']
            )
            class ConversationTitle(BaseModel):
                title: str

            try:
                prompt = template.invoke({"user_query": chat_history[0]["content"]})
                result = llm.with_structured_output(ConversationTitle).invoke(prompt)
                thread_name = result.title
            except Exception as e:
                logger.error("Failed to generate thread title: %s", e)


            existing_data = [{
                "conversation_started_at": datetime.now().isoformat(),
                "thread_id": thread_id,
                "thread_name": thread_name,
                "module": module
            }]

        # Update conversation data
        existing_data.extend(latest_exchange)

        # Save to temporary file
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w") as file:
                json.dump(existing_data, file, indent=4)
        except Exception as e:
            logger.error("Failed to write conversation to file: %s", e)
            return False
        s3_url = s3_services.upload_dict_to_s3(existing_data,
                                               S3_BUCKET,
                                               f"conversation_thread/{filename}")
        # Upload to S3
        # s3_url = s3_services.upload_file_to_s3(file_path, 'conversation_thread', filename)
        if not s3_url:
            logger.error("Failed to upload conversation to S3")
            return False

        # Create or update thread record
        try:
            # Check if thread already exists
            existing_thread = ConversationThreadModel.get_thread_by_primary_key(thread_id, module)
            if existing_thread:
                # Update existing thread
                existing_thread.update_thread(
                    text_s3_url=s3_url,
                    last_modified_by=prid,
                    thread_name=thread_name
                )
                logger.info("Updated existing thread record: %s", thread_id)
            else:
                # Create new thread
                ConversationThreadModel.create_thread(
                    thread_id=thread_id,
                    module=module,
                    prid=prid,
                    text_s3_url=s3_url,
                    last_modified_by=prid,
                    thread_name=thread_name
                )
                logger.info("Created new thread record: %s", thread_id)
        except Exception as e:
            logger.error("Failed to create/update thread record: %s", e)
            return False

        logger.info("Successfully saved conversation: thread_id=%s, s3_url=%s", thread_id, s3_url)
        return True

    except Exception as e:
        logger.exception("Unexpected error saving conversation: %s", e)
        return False
    finally:
        # Cleanup temporary file
        if 'file_path' in locals() and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except OSError as e:
                logger.warning("Failed to remove temporary file %s: %s", file_path, e)
