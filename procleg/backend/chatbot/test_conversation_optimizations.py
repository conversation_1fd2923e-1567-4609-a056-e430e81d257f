#!/usr/bin/env python3
"""
Test script to demonstrate the optimized conversation loading functionality.
This script shows how to use the new features and compares performance.
"""

import sys
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any

# Add the project root to the path
sys.path.append('/path/to/project/root')

from conversation_thread import (
    load_conversations,
    load_conversations_optimized,
    load_conversations_metadata_only,
    load_conversations_paginated,
    load_conversations_by_date_range,
    clear_conversation_cache,
    ConversationLoadOptions,
    LoadMode,
    SortBy,
    SortOrder,
    LoadMetrics
)

def test_basic_functionality():
    """Test basic backward compatibility."""
    print("=== Testing Basic Functionality (Backward Compatibility) ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    start_time = time.time()
    conversations = load_conversations(test_prid, test_module)
    end_time = time.time()
    
    print(f"Loaded {len(conversations)} conversations in {end_time - start_time:.2f}s")
    
    if conversations:
        print(f"Sample conversation: {conversations[0]['thread_id']}")
        print(f"Has metadata_only field: {'metadata_only' in conversations[0]}")
    
    return conversations

def test_metadata_only_loading():
    """Test metadata-only loading for fast listing."""
    print("\n=== Testing Metadata-Only Loading ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    start_time = time.time()
    conversations, metrics = load_conversations_metadata_only(test_prid, test_module)
    end_time = time.time()
    
    print(f"Loaded {len(conversations)} metadata entries in {end_time - start_time:.2f}s")
    print(f"Performance metrics:")
    print(f"  - Total time: {metrics.total_time:.2f}s")
    print(f"  - S3 fetch time: {metrics.s3_fetch_time:.2f}s")
    print(f"  - Processing time: {metrics.processing_time:.2f}s")
    print(f"  - Cache hits: {metrics.cache_hits}")
    print(f"  - Cache misses: {metrics.cache_misses}")
    
    if conversations:
        sample = conversations[0]
        print(f"Sample metadata:")
        print(f"  - Thread ID: {sample['thread_id']}")
        print(f"  - Thread name: {sample['thread_name']}")
        print(f"  - Created at: {sample['createdAt']}")
        print(f"  - Message count: {sample['message_count']}")
        print(f"  - Metadata only: {sample['metadata_only']}")
        print(f"  - Text messages: {len(sample['text'])}")
    
    return conversations, metrics

def test_pagination():
    """Test pagination functionality."""
    print("\n=== Testing Pagination ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    page_size = 5
    
    # Load first page
    print(f"Loading first page (page_size={page_size})...")
    conversations_page1, metrics1 = load_conversations_paginated(
        test_prid, test_module, page_size=page_size, page_offset=0
    )
    
    print(f"Page 1: {len(conversations_page1)} conversations")
    print(f"Performance: {metrics1.total_time:.2f}s")
    
    # Load second page
    print(f"Loading second page...")
    conversations_page2, metrics2 = load_conversations_paginated(
        test_prid, test_module, page_size=page_size, page_offset=1
    )
    
    print(f"Page 2: {len(conversations_page2)} conversations")
    print(f"Performance: {metrics2.total_time:.2f}s (should be faster due to caching)")
    
    # Check for overlap (there shouldn't be any)
    if conversations_page1 and conversations_page2:
        page1_ids = {conv['thread_id'] for conv in conversations_page1}
        page2_ids = {conv['thread_id'] for conv in conversations_page2}
        overlap = page1_ids.intersection(page2_ids)
        print(f"Overlap between pages: {len(overlap)} (should be 0)")
    
    return conversations_page1, conversations_page2

def test_date_filtering():
    """Test date range filtering."""
    print("\n=== Testing Date Range Filtering ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    # Filter for last 30 days
    date_from = datetime.now() - timedelta(days=30)
    date_to = datetime.now()
    
    print(f"Loading conversations from {date_from.date()} to {date_to.date()}...")
    conversations, metrics = load_conversations_by_date_range(
        test_prid, test_module, date_from=date_from, date_to=date_to, metadata_only=True
    )
    
    print(f"Found {len(conversations)} conversations in date range")
    print(f"Performance: {metrics.total_time:.2f}s")
    
    if conversations:
        # Verify date filtering worked
        for conv in conversations[:3]:  # Check first 3
            created_at = datetime.fromisoformat(conv['createdAt'].replace('Z', '+00:00'))
            print(f"  - {conv['thread_id']}: {created_at.date()}")
    
    return conversations, metrics

def test_custom_options():
    """Test custom loading options."""
    print("\n=== Testing Custom Options ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    # Create custom options
    options = ConversationLoadOptions(
        mode=LoadMode.FULL,
        page_size=10,
        page_offset=0,
        sort_by=SortBy.THREAD_NAME,
        sort_order=SortOrder.ASC,
        max_workers=5,
        retry_attempts=2,
        cache_ttl=60  # 1 minute cache
    )
    
    print("Loading with custom options:")
    print(f"  - Mode: {options.mode.value}")
    print(f"  - Sort by: {options.sort_by.value} ({options.sort_order.value})")
    print(f"  - Page size: {options.page_size}")
    print(f"  - Max workers: {options.max_workers}")
    
    conversations, metrics = load_conversations_optimized(test_prid, test_module, options)
    
    print(f"Loaded {len(conversations)} conversations")
    print(f"Performance: {metrics.total_time:.2f}s")
    
    if len(conversations) >= 2:
        # Verify sorting
        first_name = conversations[0]['thread_name'].lower()
        second_name = conversations[1]['thread_name'].lower()
        print(f"Sorting verification:")
        print(f"  - First: '{conversations[0]['thread_name']}'")
        print(f"  - Second: '{conversations[1]['thread_name']}'")
        print(f"  - Correctly sorted: {first_name <= second_name}")
    
    return conversations, metrics

def test_caching():
    """Test caching functionality."""
    print("\n=== Testing Caching ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    # Clear cache first
    cleared = clear_conversation_cache()
    print(f"Cleared {cleared} cache entries")
    
    # First load (should be cache miss)
    print("First load (cache miss expected)...")
    start_time = time.time()
    conversations1, metrics1 = load_conversations_optimized(test_prid, test_module)
    end_time = time.time()
    
    print(f"First load: {len(conversations1)} conversations in {end_time - start_time:.2f}s")
    print(f"Cache hits: {metrics1.cache_hits}, Cache misses: {metrics1.cache_misses}")
    
    # Second load (should be cache hit)
    print("Second load (cache hit expected)...")
    start_time = time.time()
    conversations2, metrics2 = load_conversations_optimized(test_prid, test_module)
    end_time = time.time()
    
    print(f"Second load: {len(conversations2)} conversations in {end_time - start_time:.2f}s")
    print(f"Cache hits: {metrics2.cache_hits}, Cache misses: {metrics2.cache_misses}")
    print(f"Speed improvement: {((metrics1.total_time - metrics2.total_time) / metrics1.total_time * 100):.1f}%")
    
    # Verify data consistency
    if conversations1 and conversations2:
        ids1 = {conv['thread_id'] for conv in conversations1}
        ids2 = {conv['thread_id'] for conv in conversations2}
        print(f"Data consistency: {ids1 == ids2}")
    
    return conversations1, conversations2

def performance_comparison():
    """Compare performance between old and new implementations."""
    print("\n=== Performance Comparison ===")
    
    test_prid = "TEST_PRID_001"
    test_module = "procurement"
    
    # Clear cache for fair comparison
    clear_conversation_cache()
    
    # Test metadata-only vs full loading
    print("Comparing metadata-only vs full loading...")
    
    # Metadata only
    start_time = time.time()
    metadata_conversations, metadata_metrics = load_conversations_metadata_only(test_prid, test_module)
    metadata_time = time.time() - start_time
    
    # Full loading
    start_time = time.time()
    full_conversations = load_conversations(test_prid, test_module)
    full_time = time.time() - start_time
    
    print(f"Results:")
    print(f"  - Metadata-only: {len(metadata_conversations)} conversations in {metadata_time:.2f}s")
    print(f"  - Full loading: {len(full_conversations)} conversations in {full_time:.2f}s")
    
    if metadata_time > 0:
        speedup = full_time / metadata_time
        print(f"  - Metadata-only is {speedup:.1f}x faster")
    
    return metadata_conversations, full_conversations

def main():
    """Run all tests."""
    print("Starting Conversation Loading Optimization Tests")
    print("=" * 60)
    
    try:
        # Run all tests
        test_basic_functionality()
        test_metadata_only_loading()
        test_pagination()
        test_date_filtering()
        test_custom_options()
        test_caching()
        performance_comparison()
        
        print("\n" + "=" * 60)
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"\nTest failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
