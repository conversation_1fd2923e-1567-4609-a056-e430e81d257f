from pynamodb.models import Model
from pynamodb.attributes import (
    UnicodeAttribute, UTCDateTimeAttribute
)
from pynamodb.indexes import GlobalSecondaryIndex, AllProjection
from datetime import datetime, timezone
from dotenv import load_dotenv
import os

load_dotenv()

CONVERSATION_THREAD_TABLE = os.getenv("CONVERSATION_THREAD_TABLE",
                            "aig-azcdi-us-alxn-procleg-conversation-thread-dev")

class PridThreadIdIndex(GlobalSecondaryIndex):
    class Meta:
        index_name = 'prid-thread_id-index'
        projection = AllProjection()

    prid = UnicodeAttribute(hash_key=True)
    thread_id = UnicodeAttribute(range_key=True)

class ConversationThreadModel(Model):
    class Meta:
        table_name = CONVERSATION_THREAD_TABLE
        region = 'us-east-1'

    # Define the attributes of the model
    thread_id = UnicodeAttribute(hash_key=True)
    module = UnicodeAttribute(range_key=True)
    prid = UnicodeAttribute()
    text_s3_url = UnicodeAttribute()
    lastModifiedBy = UnicodeAttribute()
    createdAt = UTCDateTimeAttribute()
    updatedAt = UTCDateTimeAttribute()
    thread_name = UnicodeAttribute()
    prid_thread_id_index = PridThreadIdIndex()

    @classmethod
    def create_thread(cls, thread_id: str, module: str, prid: str, text_s3_url: str,
                     last_modified_by: str, thread_name: str) -> 'ConversationThreadModel':
        thread = cls(
            thread_id=thread_id,
            module=module,
            prid=prid,
            text_s3_url=text_s3_url,
            lastModifiedBy=last_modified_by,
            thread_name=thread_name,
            createdAt=datetime.now(timezone.utc),
            updatedAt=datetime.now(timezone.utc)
        )
        thread.save()
        return thread

    @classmethod
    def get_thread_by_primary_key(cls, thread_id: str, module: str) -> 'ConversationThreadModel':
        try:
            return cls.get(thread_id, module)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_threads_by_prid(cls, prid: str) -> list['ConversationThreadModel']:
        return list(cls.prid_thread_id_index.query(prid))

    @classmethod
    def get_threads_by_prid_and_module(cls, prid: str, module: str) -> list['ConversationThreadModel']:
        try:
            results = []
            query_iterator = cls.prid_thread_id_index.query(
                prid,
                filter_condition=cls.module == module,
                scan_index_forward=True
            )

            for item in query_iterator:
                results.append(item)

            return results
        except cls.DoesNotExist:
            return []

    def update_thread(self, text_s3_url: str = None, last_modified_by: str = None,
                     thread_name: str = None) -> 'ConversationThreadModel':
        actions = []
        if text_s3_url is not None:
            actions.append(ConversationThreadModel.text_s3_url.set(text_s3_url))
        if last_modified_by is not None:
            actions.append(ConversationThreadModel.lastModifiedBy.set(last_modified_by))
        if thread_name is not None:
            actions.append(ConversationThreadModel.thread_name.set(thread_name))

        if actions:
            actions.append(ConversationThreadModel.updatedAt.set(datetime.now(timezone.utc)))
            self.update(actions=actions)
        return self

    def delete_thread(self):
        self.delete()

    @classmethod
    def delete_thread_by_primary_key(cls, thread_id: str, module: str) -> bool:
        try:
            thread = cls.get(thread_id, module)
            thread.delete()
            return True
        except cls.DoesNotExist:
            return False

    @classmethod
    def delete_threads_by_prid(cls, prid: str) -> int:
        threads = cls.get_threads_by_prid(prid)
        count = 0
        for thread in threads:
            thread.delete()
            count += 1
        return count

    @classmethod
    def get_all_threads(cls) -> list['ConversationThreadModel']:
        return list(cls.scan())

    @classmethod
    def get_threads_by_module(cls, module: str) -> list['ConversationThreadModel']:
        return list(cls.scan(ConversationThreadModel.module == module))
